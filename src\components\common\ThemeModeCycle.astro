---
export interface Props {
  i18n: any;
  lang: "fr" | "en";
}
const { i18n, lang } = Astro.props;
---

<script is:inline>
  document.addEventListener("DOMContentLoaded", () => {
    const toggleBtn = document.getElementById("theme-cycle-btn");

    if (toggleBtn) {
      toggleBtn.addEventListener("click", () => {
        console.log("click");
        toggleTheme();
      });
    }

    function toggleTheme() {
      document.documentElement.classList.toggle("theme-dark");

      const isDark = document.documentElement.classList.contains("theme-dark");
      localStorage.setItem("theme", isDark ? "dark" : "light");
    }
  });
</script>

<div class={`theme-mode-cycle`}>
  <button type="button" class="cycle-btn" id="theme-cycle-btn">
    <span class="cycle-label">{i18n.quickAccess.shortcuts.changeTheme}</span>
    <span class="cycle-dot" aria-hidden="true"></span>
    <span class="cycle-mode" data-mode-text
      >{i18n.quickAccess.theme.system}</span
    >
  </button>
</div>

<style>
  .theme-mode-cycle {
    display: inline-flex;
    overflow: hidden;
  }

  .cycle-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-4);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition:
      background-color 0.2s ease,
      box-shadow 0.2s ease,
      transform 0.1s ease;
  }
</style>
